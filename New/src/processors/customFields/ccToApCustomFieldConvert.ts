/**
 * CliniCore to AutoPatient Custom Field Conversion Utility
 *
 * Transforms CliniCore custom field structures to AutoPatient format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules:**
 * - <PERSON> boolean → AP RADIO (Yes/No options)
 * - <PERSON> select-or-custom → AP SINGLE_OPTIONS
 * - <PERSON> select (allowMultipleValues: true) → AP MULTIPLE_OPTIONS
 * - <PERSON> select (allowMultipleValues: false) → AP SINGLE_OPTIONS
 * - CC number → AP NUMERICAL
 * - CC textarea → AP LARGE_TEXT
 * - CC text/email/telephone → AP TEXT
 * - Fallback: unmappable types → AP TEXT
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type { APPostCustomfieldType, GetCCCustomField } from "@type";
import { logCustomField, logWarn } from "@/utils/logger";

// Performance-optimized sets and regex for faster lookups
const TEXT_BASED_TYPES = new Set([
	"text",
	"email",
	"telephone",
	"medication",
	"permanent-diagnoses",
	"patient-has-recommended",
]);

const NUMBERED_OPT_REGEX = /^\d+\./;
const VERSIONED_OPT_REGEX = /v\d+|version/i;
const DATE_OPT_REGEX = /\d{4}|\d{2}\/\d{2}/;
const STATUS_OPT_REGEX = /active|inactive|pending|draft/i;

/**
 * Convert CliniCore custom field to AutoPatient format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Supports dynamic option synchronization and field type evolution.
 *
 * @param ccField - CliniCore custom field object to convert
 * @returns AutoPatient custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert CC boolean field to AP radio with Yes/No options
 * const ccBooleanField: GetCCCustomField = {
 *   id: 1,
 *   name: "newsletter-wanted",
 *   label: "Newsletter erwünscht",
 *   type: "boolean",
 *   allowMultipleValues: false,
 *   allowedValues: [],
 *   // ... other properties
 * };
 *
 * const apField = ccToApCustomFieldConvert(ccBooleanField);
 * // Result: { name: "Newsletter erwünscht", dataType: "RADIO", options: ["Yes", "No"], ... }
 *
 * // Convert CC select with multiple values to AP multiple options
 * const ccSelectField: GetCCCustomField = {
 *   id: 2,
 *   name: "interests",
 *   label: "Interests",
 *   type: "select",
 *   allowMultipleValues: true,
 *   allowedValues: [
 *     { id: 1, value: "Sports", ... },
 *     { id: 2, value: "Music", ... }
 *   ],
 *   // ... other properties
 * };
 *
 * const apMultiField = ccToApCustomFieldConvert(ccSelectField);
 * // Result: { dataType: "MULTIPLE_OPTIONS", options: ["Sports", "Music"], ... }
 * ```
 */
export function ccToApCustomFieldConvert(
	ccField: GetCCCustomField,
	customName?: string,
	customFieldKey?: string,
): APPostCustomfieldType {
	logCustomField("CC→AP conversion started", ccField.name, {
		ccFieldType: ccField.type,
		allowMultiple: ccField.allowMultipleValues,
		hasAllowedValues: Boolean(ccField.allowedValues?.length),
		valueCount: ccField.allowedValues?.length || 0,
	});

	// Base field structure with common properties
	const baseField: APPostCustomfieldType = {
		name: customName || ccField.label || ccField.name, // Use custom name if provided, otherwise prefer label over name for display
		dataType: "TEXT", // Default fallback type
		placeholder: "",
		...(customFieldKey && { fieldKey: customFieldKey }), // Add fieldKey if provided
	};

	// Use a switch for more efficient type handling
	switch (ccField.type) {
		case "boolean":
			return handleCcBooleanField(ccField, baseField);
		case "select-or-custom":
			return handleCcSelectOrCustomField(ccField, baseField);
		case "select":
			return handleCcSelectField(ccField, baseField);
		case "number":
			logCustomField("CC→AP number→NUMERICAL conversion", ccField.name, {
				originalType: ccField.type,
				convertedType: "NUMERICAL",
			});
			return {
				...baseField,
				dataType: "NUMERICAL",
			};
		case "textarea":
			logCustomField("CC→AP textarea→LARGE_TEXT conversion", ccField.name, {
				originalType: ccField.type,
				convertedType: "LARGE_TEXT",
			});
			return {
				...baseField,
				dataType: "LARGE_TEXT",
			};
		default:
			if (isTextBasedField(ccField.type)) {
				logCustomField("CC→AP text-based conversion", ccField.name, {
					originalType: ccField.type,
				});
				return {
					...baseField,
					dataType: "TEXT",
				};
			}

			// Fallback for unmappable field types
			logWarn(
				`CC→AP fallback conversion for unmappable type: ${ccField.type}`,
				{
					fieldName: ccField.name,
					originalType: ccField.type,
					convertedType: "TEXT",
				},
			);

			return {
				...baseField,
				dataType: "TEXT",
			};
	}
}

/**
 * Handle CC boolean field conversion
 *
 * Converts CC boolean fields to AP RADIO with standardized Yes/No options.
 * Ensures reversible transformation back to boolean type and maintains
 * compatibility with the enhanced 2-option detection logic.
 *
 * @param ccField - CC field with boolean type
 * @param baseField - Base AP field structure
 * @returns AP RADIO field with Yes/No options
 */
function handleCcBooleanField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP boolean→RADIO conversion", ccField.name, {
		standardizedOptions: ["Yes", "No"],
		reversibleConversion: true,
		compatibleWith2OptionDetection: true,
	});

	return {
		...baseField,
		dataType: "RADIO",
		options: ["Yes", "No"],
	};
}

/**
 * Handle CC select-or-custom field conversion
 *
 * Converts CC select-or-custom fields to AP SINGLE_OPTIONS.
 * Preserves all allowed values for user selection and handles
 * dynamic option updates inherent in select-or-custom fields.
 *
 * @param ccField - CC field with select-or-custom type
 * @param baseField - Base AP field structure
 * @returns AP SINGLE_OPTIONS field with preserved options
 */
function handleCcSelectOrCustomField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	logCustomField(
		"CC→AP select-or-custom→SINGLE_OPTIONS conversion",
		ccField.name,
		{
			optionCount: options.length,
			options: options,
			dynamicOptionsSupported: true,
			customOptionsAllowed: true,
		},
	);

	// Log about dynamic nature of select-or-custom fields
	logCustomField("Dynamic option capability detected", ccField.name, {
		fieldType: "select-or-custom",
		supportsUserAddedOptions: true,
		recommendation: "monitor_for_new_custom_options",
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Handle CC select field conversion
 *
 * Converts CC select fields to either AP MULTIPLE_OPTIONS or AP SINGLE_OPTIONS
 * based on the allowMultipleValues setting. Preserves selection behavior
 * and handles dynamic option updates.
 *
 * @param ccField - CC field with select type
 * @param baseField - Base AP field structure
 * @returns AP field with appropriate selection type
 */
function handleCcSelectField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP select→MULTIPLE_OPTIONS conversion", ccField.name, {
			optionCount: options.length,
			allowMultiple: true,
			dynamicOptionsSupported: true,
		});

		// Check for dynamic option patterns
		detectDynamicOptionPatterns(options, ccField.name);

		return {
			...baseField,
			dataType: "MULTIPLE_OPTIONS",
			options: options,
		};
	}

	// Single selection converts to SINGLE_OPTIONS
	logCustomField("CC→AP select→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		allowMultiple: false,
		properFieldTypeSemantics: true,
	});

	// Check for dynamic option patterns
	detectDynamicOptionPatterns(options, ccField.name);

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Check if CC field type is text-based
 *
 * Identifies CC field types that should convert to AP TEXT fields.
 * Supports various text input types for comprehensive coverage.
 *
 * @param fieldType - CC field type to check
 * @returns True if field type is text-based
 *
 * @example
 * ```typescript
 * isTextBasedField("text") // true
 * isTextBasedField("email") // true
 * isTextBasedField("telephone") // true
 * isTextBasedField("textarea") // false (now handled separately as LARGE_TEXT)
 * isTextBasedField("number") // false (now handled separately as NUMERICAL)
 * isTextBasedField("select") // false
 * isTextBasedField("boolean") // false
 * ```
 */
function isTextBasedField(fieldType: string): boolean {
	return TEXT_BASED_TYPES.has(fieldType);
}

/**
 * Extract option values from CC allowedValues array
 *
 * Safely extracts string values from CC allowedValues structure.
 * Handles missing or malformed data gracefully.
 *
 * @param allowedValues - CC allowedValues array
 * @returns Array of option strings for AP field
 *
 * @example
 * ```typescript
 * const ccValues = [
 *   { id: 1, value: "Option A", ... },
 *   { id: 2, value: "Option B", ... }
 * ];
 *
 * extractOptionsFromAllowedValues(ccValues) // ["Option A", "Option B"]
 * extractOptionsFromAllowedValues([]) // []
 * extractOptionsFromAllowedValues(undefined) // []
 * ```
 */
function extractOptionsFromAllowedValues(
	allowedValues: GetCCCustomField["allowedValues"],
): string[] {
	if (!allowedValues || !Array.isArray(allowedValues)) {
		return [];
	}

	return allowedValues
		.map((item) => item.value)
		.filter(
			(value): value is string => typeof value === "string" && value.length > 0,
		);
}

/**
 * Detect and log potential dynamic option patterns in CC fields
 *
 * Analyzes option patterns to identify fields that may support dynamic
 * option addition, removal, or modification after initial creation.
 * Helps with monitoring field evolution and synchronization planning.
 *
 * @param options - Current option array
 * @param fieldName - Field name for logging
 */
function detectDynamicOptionPatterns(
	options: string[],
	fieldName: string,
): void {
	const optionCount = options.length;
	if (optionCount === 0) {
		logCustomField("Dynamic option pattern detected in CC field", fieldName, {
			hasNumberedOptions: false,
			hasVersionedOptions: false,
			hasDateOptions: false,
			hasStatusOptions: false,
			hasEmptyOptions: true,
			optionCount: 0,
			recommendation: "monitor_for_option_updates",
			syncConsideration: "field_may_evolve_over_time",
		});
		return;
	}

	let hasNumberedOptions = false;
	let hasVersionedOptions = false;
	let hasDateOptions = false;
	let hasStatusOptions = false;

	for (const opt of options) {
		// Use short-circuiting to avoid re-testing if already found
		hasNumberedOptions = hasNumberedOptions || NUMBERED_OPT_REGEX.test(opt);
		hasVersionedOptions = hasVersionedOptions || VERSIONED_OPT_REGEX.test(opt);
		hasDateOptions = hasDateOptions || DATE_OPT_REGEX.test(opt);
		hasStatusOptions = hasStatusOptions || STATUS_OPT_REGEX.test(opt);

		// Early exit if all patterns are found
		if (
			hasNumberedOptions &&
			hasVersionedOptions &&
			hasDateOptions &&
			hasStatusOptions
		) {
			break;
		}
	}

	if (
		hasNumberedOptions ||
		hasVersionedOptions ||
		hasDateOptions ||
		hasStatusOptions
	) {
		logCustomField("Dynamic option pattern detected in CC field", fieldName, {
			hasNumberedOptions,
			hasVersionedOptions,
			hasDateOptions,
			hasStatusOptions,
			hasEmptyOptions: false,
			optionCount,
			recommendation: "monitor_for_option_updates",
			syncConsideration: "field_may_evolve_over_time",
		});
	}


}

export default ccToApCustomFieldConvert;
