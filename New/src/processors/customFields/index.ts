/**
 * Custom Fields Processing Module
 *
 * Comprehensive custom field processing utilities for AutoPatient and CliniCore platforms.
 * Provides complete field synchronization, matching, creation, conflict detection, and
 * database operations with intelligent algorithms and comprehensive error handling.
 *
 * **Core Modules:**
 * - `fieldSynchronizer`: Main synchronization engine with comprehensive field matching
 * - `fieldMatcher`: Intelligent field matching algorithms with fuzzy matching
 * - `fieldCreator`: Bidirectional field creation with error handling
 * - `conflictDetector`: Standard field and existing field conflict detection
 * - `databaseOperations`: Database mapping operations with upsert logic
 * - `types`: TypeScript interfaces and type definitions
 *
 * **Conversion Utilities:**
 * - `apToCcCustomFieldConvert`: Convert AutoPatient fields to CliniCore format
 * - `ccToApCustomFieldConvert`: Convert CliniCore fields to AutoPatient format
 *
 * **Key Features:**
 * - Comprehensive bidirectional field synchronization
 * - Intelligent field matching with multiple strategies
 * - Automatic field creation with conflict detection
 * - Standard field mapping and blocklist filtering
 * - Database operations with upsert logic and error handling
 * - Detailed logging and statistics tracking
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for production environments
 *
 * @example
 * ```typescript
 * import {
 *   synchronizeCustomFields,
 *   fieldsMatch,
 *   createApFieldInCc,
 *   detectFieldConflicts,
 *   storeMappingForCreatedFields
 * } from '@processors/customFields';
 *
 * // Complete field synchronization
 * const syncResult = await synchronizeCustomFields("req-123");
 * console.log(`Synchronized ${syncResult.matchedCount} field pairs`);
 *
 * // Individual field matching
 * const isMatch = fieldsMatch(apField, ccField);
 *
 * // Field creation with conflict detection
 * const conflicts = detectFieldConflicts(sourceField, targetFields, "ap", "cc", "req-123");
 * if (!conflicts.hasConflict) {
 *   const createdField = await createApFieldInCc(apField, "req-123");
 * }
 * ```
 *
 * @since 1.0.0
 * @version 3.0.0
 */

// Field conversion utilities (legacy compatibility)
export { default as apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
export { default as ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";
// Conflict detection utilities
export {
	areFieldTypesCompatible,
	checkApFieldCreationBlocklist,
	checkForExistingCustomFieldConflict,
	checkForStandardFieldMapping,
	detectFieldConflicts,
	generateUniqueFieldName,
} from "./conflictDetector";
// Database operations
export {
	bulkUpsertFieldMappings,
	storeMappingForCreatedFields,
	storeStandardFieldMapping,
	updateFieldMapping,
} from "./databaseOperations";
// Field creation operations
export {
	createApFieldInCc,
	createCcFieldInAp,
	createFieldWithResult,
} from "./fieldCreator";
// Field matching utilities
export {
	fieldsMatch,
	findExistingCustomField,
	findMatchingField,
} from "./fieldMatcher";
// Core synchronization functionality
export { synchronizeCustomFields } from "./fieldSynchronizer";
// Type definitions
export type {
	CustomFieldInsert,
	CustomFieldSyncResponse,
	FieldConflictResult,
	FieldCreationResult,
	Platform,
} from "./types";
export { FieldMatchStrategy } from "./types";
