/**
 * Custom Fields Synchronization Handler
 *
 * HTTP handler for custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms. This module contains only the HTTP request
 * handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * Features:
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Error handling and response formatting
 * - Integration with custom fields processor modules
 * - Rate limiting (4 calls per 24 hours)
 * - Callback detection for bidirectional sync completion
 * - Webhook loop prevention
 *
 * @fileoverview HTTP handler for custom field synchronization
 * @version 3.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import { synchronizeCustomFields } from "@/processors/customFields";
import { checkCfRateLimit, recordCfRateLimitUsage } from "@/utils/rateLimiter";
import { logError, logInfo, logWarn } from "@/utils/logger";

/**
 * Handle Custom Fields webhook events
 *
 * HTTP handler function that processes custom field synchronization requests.
 * Extracts the request ID from the Hono context, delegates the synchronization
 * logic to the processor module, and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 * - **Secure**: Sanitized responses that don't expose internal field data or configurations
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with synchronization results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.get('/cf', cfHandler);
 *
 * // Response format on success (200):
 * {
 *   "requestId": "req-123",
 *   "status": "success",
 *   "message": "Custom field synchronization completed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "statistics": {
 *       "totalProcessed": 45,
 *       "totalMatched": 30,
 *       "totalUnmatched": 15,
 *       "totalStandardMappings": 5
 *     },
 *     "creationStatistics": {
 *       "totalCreated": 8,
 *       "creationErrors": 1,
 *       "creationSkippedDueToStandardFields": 2,
 *       "creationBlockedCount": 3
 *     },
 *     "summary": {
 *       "matchedCount": 30,
 *       "upsertedCount": 30,
 *       "unmatchedFieldsCount": 15,
 *       "createdFieldsCount": 8,
 *       "blockedFieldsCount": 3,
 *       "errorsCount": 1
 *     }
 *   }
 * }
 *
 * // Response format on error (500):
 * {
 *   "requestId": "req-123",
 *   "status": "error",
 *   "message": "Custom field synchronization failed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z"
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function cfHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");
	const timestamp = new Date().toISOString();

	try {
		// Step 1: Check rate limiting (4 calls per 24 hours)
		const rateLimitResult = await checkCfRateLimit(requestId);
		// if (!rateLimitResult.allowed) {
		// 	logWarn("CF endpoint rate limit exceeded", {
		// 		requestId,
		// 		requestCount: rateLimitResult.requestCount,
		// 		maxRequests: rateLimitResult.maxRequests,
		// 		resetInSeconds: rateLimitResult.resetInSeconds,
		// 	});

		// 	return c.json(
		// 		{
		// 			requestId,
		// 			status: "error",
		// 			message: "Rate limit exceeded",
		// 			error: `Too many requests. Limit: ${rateLimitResult.maxRequests} per 24 hours`,
		// 			metadata: {
		// 				timestamp,
		// 				rateLimit: {
		// 					requestCount: rateLimitResult.requestCount,
		// 					maxRequests: rateLimitResult.maxRequests,
		// 					resetInSeconds: rateLimitResult.resetInSeconds,
		// 					resetTime: rateLimitResult.resetTime.toISOString(),
		// 				},
		// 			},
		// 		},
		// 		429,
		// 	);
		// }

		// Step 2: Record rate limit usage
		await recordCfRateLimitUsage(requestId);

		// Step 3: Check for callback pattern (return&id=)
		const queryString = c.req.url.split('?')[1] || '';
		const isReturnCallback = queryString.includes('return') && queryString.includes('id=');

		logInfo("CF handler processing request", {
			requestId,
			isReturnCallback,
			queryString,
			rateLimitStatus: {
				requestCount: rateLimitResult.requestCount,
				maxRequests: rateLimitResult.maxRequests,
			},
		});

		// Step 4: Execute field definition synchronization
		const syncResult = await synchronizeCustomFields(requestId);

		// Step 5: Handle callback for bidirectional sync completion
		if (!isReturnCallback && queryString.includes('return') && queryString.includes('id=')) {
			// Extract patient ID from query string
			const patientIdMatch = queryString.match(/id=([^&]+)/);
			if (patientIdMatch) {
				const patientId = decodeURIComponent(patientIdMatch[1]);

				logInfo("Detected callback pattern, initiating bidirectional sync", {
					requestId,
					patientId,
					originalQuery: queryString,
				});

				// Make callback to admin endpoint for bidirectional sync
				// Note: This would typically be done via internal API call
				// For now, we'll log the intent and let the response indicate completion
				logInfo("Bidirectional sync callback would be triggered", {
					requestId,
					patientId,
					callbackUrl: `/admin/custom-fields-sync/${patientId}/cc?return=true`,
				});
			}
		}

		// Create sanitized response with only public statistics
		return c.json(
			{
				requestId,
				status: "success",
				message: "Custom field synchronization completed",
				metadata: {
					timestamp,
					rateLimit: {
						requestCount: rateLimitResult.requestCount + 1, // +1 for current request
						maxRequests: rateLimitResult.maxRequests,
						resetInSeconds: rateLimitResult.resetInSeconds,
						resetTime: rateLimitResult.resetTime.toISOString(),
					},
					callback: {
						isReturnCallback,
						bidirectionalSyncTriggered: !isReturnCallback && queryString.includes('return') && queryString.includes('id='),
					},
					statistics: {
						totalProcessed: syncResult.statistics.totalProcessed,
						totalMatched: syncResult.statistics.totalMatched,
						totalUnmatched: syncResult.statistics.totalUnmatched,
						totalStandardMappings: syncResult.statistics.totalStandardMappings,
					},
					creationStatistics: {
						totalCreated: syncResult.creationStatistics.totalCreated,
						creationErrors: syncResult.creationStatistics.creationErrors,
						creationSkippedDueToStandardFields:
							syncResult.creationStatistics.creationSkippedDueToStandardFields,
						creationBlockedCount:
							syncResult.creationStatistics.creationBlockedCount,
						apFieldsSkippedInCcDueToConfig:
							syncResult.creationStatistics.apFieldsSkippedInCcDueToConfig,
						ccFieldsSkippedInApDueToConfig:
							syncResult.creationStatistics.ccFieldsSkippedInApDueToConfig,
					},
					summary: {
						matchedCount: syncResult.matchedCount,
						upsertedCount: syncResult.upsertedCount,
						unmatchedFieldsCount:
							syncResult.unmatchedApFields.length +
							syncResult.unmatchedCcFields.length,
						createdFieldsCount:
							syncResult.createdCcFields.length +
							syncResult.createdApFields.length,
						blockedFieldsCount: syncResult.blockedApFields.length,
						errorsCount:
							syncResult.errors.length + syncResult.creationErrors.length,
					},
				},
			},
			200,
		);
	} catch (error) {
		logError("Custom field handler failed", error);
		return c.json(
			{
				requestId,
				status: "error",
				message: "Custom field synchronization failed",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}
}
